import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { v2 as cloudinary } from 'cloudinary';
import { initCloudinary } from './cloudinary.config';

@Injectable()
export class CloudinaryService {
  private readonly cloudinary;
  private readonly logger = new Logger(CloudinaryService.name);

  constructor(private readonly configService: ConfigService) {
    try {
      this.cloudinary = initCloudinary(configService);
    } catch (error) {
      this.cloudinary = cloudinary;
    }
  }

  /**
   * Upload an image to Cloudinary
   * @param file - The file buffer to upload
   * @param folder - Optional folder name to organize uploads
   * @returns Promise with the upload result containing the URL
   */
  async uploadImage(
    file: Buffer,
    folder?: string,
  ): Promise<{ url: string; public_id: string }> {
    try {
      // Verify Cloudinary configuration
      const config = this.cloudinary.config();
      if (!config.api_key) {
        throw new Error(
          'Cloudinary configuration is incomplete. Please check your environment variables.',
        );
      }

      // Detect file type from the first few bytes
      let mimeType = 'image/jpeg'; // Default
      let isSvg = false;

      // Check for SVG signature
      const fileStart = file.slice(0, 50).toString().toLowerCase();
      if (
        fileStart.includes('<svg') ||
        (fileStart.includes('<?xml') && fileStart.includes('<svg'))
      ) {
        mimeType = 'image/svg+xml';
        isSvg = true;
      }
      // Check for PNG signature
      else if (
        file[0] === 0x89 &&
        file[1] === 0x50 &&
        file[2] === 0x4e &&
        file[3] === 0x47
      ) {
        mimeType = 'image/png';
      }

      const uploadOptions: any = {
        resource_type: isSvg ? 'image' : 'auto',
      };

      if (isSvg) {
        // Force SVG to be treated as an image, not as a raw file
        uploadOptions.format = 'svg';
      }

      if (folder) {
        uploadOptions.folder = folder;
      }

      // Convert buffer to base64 string for Cloudinary upload
      const base64String = Buffer.from(file).toString('base64');
      const dataURI = `data:${mimeType};base64,${base64String}`;

      this.logger.log(`Uploading file with MIME type: ${mimeType}`);
      this.logger.log(`Upload options: ${JSON.stringify(uploadOptions)}`);

      const result = await this.cloudinary.uploader.upload(
        dataURI,
        uploadOptions,
      );

      this.logger.log(`File uploaded successfully. URL: ${result.secure_url}`);
      this.logger.log(
        `Resource type: ${result.resource_type}, Format: ${result.format || 'none'}`,
      );

      return {
        url: result.secure_url,
        public_id: result.public_id,
      };
    } catch (error) {
      this.logger.error(
        `Failed to upload image to Cloudinary: ${error.message}`,
      );
      throw new Error(`Failed to upload image to Cloudinary: ${error.message}`);
    }
  }

  /**
   * Delete an image from Cloudinary
   * @param publicId - The public ID of the image to delete
   * @returns Promise with the deletion result
   */
  async deleteImage(publicId: string): Promise<any> {
    try {
      this.logger.debug(
        `Deleting image from Cloudinary with public ID: ${publicId}`,
      );
      const result = await this.cloudinary.uploader.destroy(publicId);
      this.logger.debug('Image deleted successfully from Cloudinary');
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to delete image from Cloudinary: ${error.message}`,
      );
      throw new Error(
        `Failed to delete image from Cloudinary: ${error.message}`,
      );
    }
  }
}
