# Cloudinary Module

This module provides integration with Cloudinary for image storage and management in your NestJS application.

## Setup

1. Make sure you have the required environment variables set in your `.env` file:

```
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
```

2. Import the `CloudinaryModule` in your module:

```typescript
import { Module } from '@nestjs/common';
import { CloudinaryModule } from '@app/common/config/cloudinary';

@Module({
  imports: [
    CloudinaryModule,
    // other imports...
  ],
  // ...
})
export class YourModule {}
```

## Usage

### In a Controller

```typescript
import { Controller, Post, UploadedFile, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { CloudinaryService } from '@app/common/config/cloudinary';
import { uploadFileToCloudinary } from '@app/common/utils';

@Controller('uploads')
export class UploadsController {
  constructor(private readonly cloudinaryService: CloudinaryService) {}

  @Post('image')
  @UseInterceptors(FileInterceptor('file'))
  async uploadImage(@UploadedFile() file: Express.Multer.File) {
    const result = await uploadFileToCloudinary(
      file.buffer,
      this.cloudinaryService,
      'optional-folder-name',
    );

    return {
      message: 'Image uploaded successfully',
      result: {
        url: result.url,
        publicId: result.public_id,
      },
    };
  }
}
```

### In a Service

```typescript
import { Injectable } from '@nestjs/common';
import { CloudinaryService } from '@app/common/config/cloudinary';

@Injectable()
export class YourService {
  constructor(private readonly cloudinaryService: CloudinaryService) {}

  async processImage(imageBuffer: Buffer): Promise<string> {
    const result = await this.cloudinaryService.uploadImage(
      imageBuffer,
      'your-folder-name',
    );
    
    return result.url;
  }
  
  async deleteImage(publicId: string): Promise<void> {
    await this.cloudinaryService.deleteImage(publicId);
  }
}
```

## Features

- Upload images to Cloudinary
- Delete images from Cloudinary
- Organize uploads in folders
- Secure uploads with your Cloudinary credentials
