import { Logger } from '@nestjs/common';

const logger = new Logger('CloudinaryUrlUtils');

/**
 * Extract the public ID from a Cloudinary URL
 * @param url - The Cloudinary URL
 * @returns The public ID or null if it couldn't be extracted
 */
export const extractPublicIdFromUrl = (url: string): string | null => {
  if (!url) {
    return null;
  }

  try {
    // Handle different Cloudinary URL formats

    // Format: https://res.cloudinary.com/{cloud_name}/image/upload/v{version}/{folder}/{public_id}.{extension}
    // or: https://res.cloudinary.com/{cloud_name}/raw/upload/v{version}/{folder}/{public_id}
    const regex = /\/(?:image|raw)\/upload\/(?:v\d+\/)?(.+?)(?:\.\w+)?$/;
    const match = url.match(regex);

    if (match && match[1]) {
      return match[1];
    }

    logger.warn(`Could not extract public ID from URL: ${url}`);
    return null;
  } catch (error) {
    logger.error(`Error extracting public ID from URL: ${error.message}`);
    return null;
  }
};
