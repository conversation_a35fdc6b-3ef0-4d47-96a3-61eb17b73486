import {
  Controller,
  Post,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { CloudinaryService } from '../config/cloudinary';
import { uploadFileToCloudinary } from '../utils';

/**
 * This is an example controller showing how to use the Cloudinary service
 * for image uploads. You can use this as a reference for implementing
 * image uploads in your actual controllers.
 */
@Controller('uploads')
export class UploadsController {
  constructor(private readonly cloudinaryService: CloudinaryService) {}

  @Post('image')
  @UseInterceptors(FileInterceptor('file'))
  async uploadImage(@UploadedFile() file: Express.Multer.File) {
    // The folder parameter is optional and can be used to organize uploads
    const result = await uploadFileToCloudinary(
      file.buffer,
      this.cloudinaryService,
      'portfolio', // Optional folder name
    );

    return {
      message: 'Image uploaded successfully',
      result: {
        url: result.url,
        publicId: result.public_id,
      },
    };
  }
}

/**
 * To use this controller, you need to:
 *
 * 1. Import the CloudinaryModule in your module:
 *
 * @Module({
 *   imports: [
 *     CloudinaryModule,
 *     // other imports...
 *   ],
 *   controllers: [UploadsController],
 *   // ...
 * })
 * export class YourModule {}
 *
 * 2. Make sure you have the required environment variables set:
 *    - CLOUDINARY_CLOUD_NAME
 *    - CLOUDINARY_API_KEY
 *    - CLOUDINARY_API_SECRET
 *
 * 3. Use the controller in your application to handle file uploads
 */
