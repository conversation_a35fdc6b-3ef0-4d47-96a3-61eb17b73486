import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type UserDocument = User & Document;

@Schema({
  timestamps: true,
  versionKey: false,
})
export class User extends Document {
  @Prop({ type: String, trim: true })
  name: string;

  @Prop({ type: String, trim: true })
  password: string;

  @Prop({
    type: String,
    trim: true,
    unique: true,
    index: true,
  })
  email: string;

  @Prop({ type: Date, trim: true })
  dob: Date;

  @Prop({ type: String, trim: true })
  phone: string;

  @Prop({ type: String, trim: true })
  coverImage: string;

  @Prop({ type: String, trim: true })
  profileImage: string;

  @Prop({ type: Boolean, default: true, index: true })
  status: boolean;

  @Prop({ type: Date })
  createdAt: Date;

  @Prop({ type: Date })
  updatedAt: Date;
}

const schema = SchemaFactory.createForClass(User);
export const UserSchema = schema;
