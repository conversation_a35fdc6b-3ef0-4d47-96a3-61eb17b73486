import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { User } from '@app/db/portfolio-db/schemas/user.schema';
import { Role } from '@app/db/portfolio-db/schemas/role.schema';

export type UserRoleDocument = UserRole & Document;

@Schema({
  timestamps: true,
  versionKey: false,
})
export class UserRole extends Document {
  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  })
  user: User;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Role',
    required: true,
    index: true,
  })
  role: Role;

  @Prop({ type: [String], default: [] })
  additionalPermissions: string[];

  @Prop({ type: [String], default: [] })
  deniedPermissions: string[];

  @Prop({ type: Boolean, default: true, index: true })
  isActive: boolean;

  @Prop({ type: Boolean, default: false })
  isDefault: boolean;

  @Prop({ type: Date, index: true })
  expiresAt: Date;

  @Prop({ type: Date })
  assignedAt: Date;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  assignedBy: User;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  updatedBy: User;

  @Prop({ type: String, trim: true })
  notes: string;

  @Prop({ type: Date })
  createdAt: Date;

  @Prop({ type: Date })
  updatedAt: Date;
}

const schema = SchemaFactory.createForClass(UserRole);

// Add compound indexes for better performance
schema.index({ user: 1, role: 1 }, { unique: true });
schema.index({ user: 1, isActive: 1 });
schema.index({ role: 1, isActive: 1 });
schema.index({ user: 1, isDefault: 1 });
schema.index({ expiresAt: 1 });
schema.index({ user: 1, expiresAt: 1 });
schema.index({ assignedAt: -1 });

export const UserRoleSchema = schema;
