import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { User } from '@app/db/portfolio-db/schemas/user.schema';

export type AuthDocument = Auth & Document;

@Schema({
  timestamps: true,
  versionKey: false,
})
export class Auth extends Document {
  @Prop({ type: Array, trim: true })
  tokens: string[];

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User' })
  user: User;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;
}

const schema = SchemaFactory.createForClass(Auth);
export const AuthSchema = schema;
