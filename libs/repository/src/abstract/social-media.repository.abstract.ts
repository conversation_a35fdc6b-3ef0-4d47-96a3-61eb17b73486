import { IRepository } from '../adapters';
import { SocialMediaEntity } from '@app/repository';

export abstract class ISocialMediaRepository extends IRepository<SocialMediaEntity> {
  abstract findAllWithPagination(
    filter: Partial<SocialMediaEntity>,
    page?: number,
    limit?: number,
  ): Promise<{
    data: SocialMediaEntity[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }>;
}
