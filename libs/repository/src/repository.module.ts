import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { RepositoryService } from './repository.service';
import { dbConfig } from '@app/repository/constants';
import { IDataServices, ISession } from '@app/repository/adapters';
import { DbModule } from '@app/db/db.module';
import { PortfolioDbSession } from '@app/db/portfolio-db/portfolio-db.session';
import {
  Auth,
  AuthSchema,
  Bio,
  BioSchema,
  Company,
  CompanySchema,
  Config,
  ConfigSchema,
  Contact,
  ContactSchema,
  Education,
  EducationSchema,
  Interest,
  InterestSchema,
  Objective,
  ObjectiveSchema,
  Project,
  ProjectSchema,
  Role,
  RoleSchema,
  Skill,
  SkillSchema,
  SocialMedia,
  SocialMediaSchema,
  User,
  UserRole,
  UserRoleSchema,
  UserSchema,
  VisitorLog,
  VisitorLogSchema,
} from '@app/db/portfolio-db/schemas';

@Module({
  imports: [
    DbModule,
    MongooseModule.forFeature(
      [
        {
          name: Auth.name,
          schema: AuthSchema,
        },
        {
          name: Bio.name,
          schema: BioSchema,
        },
        {
          name: Company.name,
          schema: CompanySchema,
        },
        {
          name: Config.name,
          schema: ConfigSchema,
        },
        {
          name: Contact.name,
          schema: ContactSchema,
        },
        {
          name: Education.name,
          schema: EducationSchema,
        },
        {
          name: Interest.name,
          schema: InterestSchema,
        },
        {
          name: Objective.name,
          schema: ObjectiveSchema,
        },
        {
          name: Project.name,
          schema: ProjectSchema,
        },
        {
          name: Role.name,
          schema: RoleSchema,
        },
        {
          name: Skill.name,
          schema: SkillSchema,
        },
        {
          name: SocialMedia.name,
          schema: SocialMediaSchema,
        },
        {
          name: User.name,
          schema: UserSchema,
        },
        {
          name: UserRole.name,
          schema: UserRoleSchema,
        },
      ],
      `${dbConfig()['MONGO_MAIN_DB_CONNECTION_NAME']}`,
    ),
    MongooseModule.forFeature(
      [
        {
          name: VisitorLog.name,
          schema: VisitorLogSchema,
        },
        // {
        //   name: ThirdPartyLog.name,
        //   schema: ThirdPartyLogSchema,
        // },
      ],
      `${dbConfig()['MONGO_LOG_DB_CONNECTION_NAME']}`,
    ),
  ],
  providers: [
    {
      provide: IDataServices,
      useClass: RepositoryService,
    },
    {
      scope: Scope.REQUEST,
      provide: ISession,
      useClass: PortfolioDbSession,
    },
  ],
  exports: [IDataServices, ISession],
})
export class RepositoryModule {}
