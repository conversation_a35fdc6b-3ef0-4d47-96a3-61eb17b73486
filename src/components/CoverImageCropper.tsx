'use client'

import { useCallback, useState } from 'react'

import <PERSON><PERSON><PERSON> from 'react-easy-crop'
import { Box, Button, Dialog, DialogActions, DialogContent, Slider, Typography } from '@mui/material'

// Import CSS from the package
import 'react-easy-crop/react-easy-crop.css'

// Define the Area type for cropped area
type Area = {
  x: number
  y: number
  width: number
  height: number
}

interface CoverImageCropperProps {
  image: string
  open: boolean
  onClose: () => void
  onCropComplete: (croppedImageBlob: Blob) => void
  aspectRatio?: number // Default to 3:1 for cover images
}

// Function to create an image from a source
const createImage = (url: string): Promise<HTMLImageElement> =>
  new Promise((resolve, reject) => {
    const image = new Image()

    image.addEventListener('load', () => resolve(image))
    image.addEventListener('error', error => reject(error))
    image.src = url
  })

// Function to get the cropped image
async function getCroppedImg(imageSrc: string, pixelCrop: Area, rotation = 0): Promise<Blob> {
  const image = await createImage(imageSrc)
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')

  if (!ctx) {
    throw new Error('Canvas context is not available')
  }

  // Set canvas dimensions to match the cropped area
  canvas.width = pixelCrop.width
  canvas.height = pixelCrop.height

  // Draw the cropped image
  ctx.drawImage(
    image,
    pixelCrop.x,
    pixelCrop.y,
    pixelCrop.width,
    pixelCrop.height,
    0,
    0,
    pixelCrop.width,
    pixelCrop.height
  )

  // Convert canvas to blob
  return new Promise((resolve, reject) => {
    canvas.toBlob(blob => {
      if (blob) {
        resolve(blob)
      } else {
        reject(new Error('Canvas is empty'))
      }
    }, 'image/jpeg', 0.95)
  })
}

const CoverImageCropper = ({ 
  image, 
  open, 
  onClose, 
  onCropComplete, 
  aspectRatio = 3 // 3:1 aspect ratio for cover images
}: CoverImageCropperProps) => {
  const [crop, setCrop] = useState({ x: 0, y: 0 })
  const [zoom, setZoom] = useState(1)
  const [rotation, setRotation] = useState(0)
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null)

  const onCropChange = useCallback((crop: { x: number; y: number }) => {
    setCrop(crop)
  }, [])

  const onZoomChange = useCallback((zoom: number) => {
    setZoom(zoom)
  }, [])

  const onRotationChange = useCallback((rotation: number) => {
    setRotation(rotation)
  }, [])

  const onCropCompleteHandler = useCallback(
    (croppedArea: Area, croppedAreaPixels: Area) => {
      setCroppedAreaPixels(croppedAreaPixels)
    },
    []
  )

  const handleCropImage = useCallback(async () => {
    try {
      if (croppedAreaPixels) {
        const croppedImage = await getCroppedImg(image, croppedAreaPixels, rotation)

        onCropComplete(croppedImage)
        onClose()
      }
    } catch (e) {
      console.error(e)
    }
  }, [croppedAreaPixels, image, rotation, onCropComplete, onClose])

  const handleClose = () => {
    // Reset state when closing
    setCrop({ x: 0, y: 0 })
    setZoom(1)
    setRotation(0)
    setCroppedAreaPixels(null)
    onClose()
  }

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogContent
        sx={{
          position: 'relative',
          height: '500px',
          padding: 0,
          backgroundColor: '#333'
        }}
      >
        <Cropper
          image={image}
          crop={crop}
          zoom={zoom}
          rotation={rotation}
          aspect={aspectRatio}
          onCropChange={onCropChange}
          onZoomChange={onZoomChange}
          onCropComplete={onCropCompleteHandler}
          cropShape="rect"
          showGrid={true}
        />
      </DialogContent>
      
      <Box sx={{ px: 3, pt: 2, pb: 1 }}>
        <Typography variant='body2' gutterBottom>
          Zoom
        </Typography>
        <Slider
          value={zoom}
          min={1}
          max={3}
          step={0.1}
          aria-labelledby='Zoom'
          onChange={(e, zoom) => onZoomChange(zoom as number)}
          sx={{ mb: 2 }}
        />
        
        <Typography variant='body2' gutterBottom>
          Rotation
        </Typography>
        <Slider
          value={rotation}
          min={0}
          max={360}
          step={1}
          aria-labelledby='Rotation'
          onChange={(e, rotation) => setRotation(rotation as number)}
          sx={{ mb: 2 }}
        />
      </Box>

      <DialogActions sx={{ px: 3, pb: 2 }}>
        <Button onClick={handleClose} color='secondary'>
          Cancel
        </Button>
        <Button onClick={handleCropImage} variant='contained' color='primary'>
          Apply Crop
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default CoverImageCropper
