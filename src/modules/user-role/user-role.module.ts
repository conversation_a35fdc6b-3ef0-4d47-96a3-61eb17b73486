import { Modu<PERSON> } from '@nestjs/common';
import { UserRoleService } from './user-role.service';
import { UserRoleController } from './user-role.controller';
import { RepositoryModule } from '@app/repository/repository.module';
import { AdminOnlyGuard, PermissionsGuard } from '@app/common/guards';
import { AdminAccessChecker } from './debug/check-admin-access';
import { AdminSetupService } from './scripts/setup-admin';

@Module({
  imports: [RepositoryModule],
  controllers: [UserRoleController],
  providers: [
    UserRoleService,
    AdminOnlyGuard,
    PermissionsGuard,
    AdminAccessChecker,
    AdminSetupService,
  ],
  exports: [UserRoleService, AdminOnlyGuard, PermissionsGuard],
})
export class UserRoleModule {}
