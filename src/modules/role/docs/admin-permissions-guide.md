# Admin Permissions Guide

## Overview
The role system now automatically assigns ALL permissions to admin-level roles by default. This ensures that admin users have complete access to all system functionality.

## Automatic Permission Assignment

### Admin Roles
When creating or updating a role with `level: "admin"`:
- ✅ **ALL permissions are automatically assigned** (66+ permissions)
- ✅ **Priority is set to 100** (highest priority)
- ✅ **Cannot be overridden** - admin roles always get all permissions

### Other Role Levels
- **Moderator**: Gets predefined moderator permissions
- **User**: Gets predefined user permissions  
- **Viewer**: Gets predefined viewer permissions

## API Examples

### Creating an Admin Role
```bash
POST /v1/roles
Authorization: Bearer <admin-token>

{
  "name": "System Administrator",
  "description": "Full system access",
  "level": "admin"
  // permissions will be automatically set to ALL permissions
  // priority will be automatically set to 100
}
```

### Creating Other Role Levels
```bash
POST /v1/roles
Authorization: Bearer <admin-token>

{
  "name": "Content Manager", 
  "description": "Can manage content",
  "level": "moderator"
  // permissions will be set to default moderator permissions
  // priority will be set to 75
}
```

### Custom Permissions (Non-Admin)
```bash
POST /v1/roles
Authorization: Bearer <admin-token>

{
  "name": "Custom Role",
  "description": "Custom permissions",
  "level": "user",
  "permissions": ["project:create", "project:update"]
  // Will use specified permissions instead of defaults
}
```

## Permission Levels

### Admin (Level: "admin")
- **Permissions**: ALL 66+ permissions automatically
- **Priority**: 100
- **Access**: Complete system access

### Moderator (Level: "moderator") 
- **Permissions**: ~30 permissions (content management, user viewing, etc.)
- **Priority**: 75
- **Access**: Content and user management

### User (Level: "user")
- **Permissions**: ~15 permissions (read access, basic operations)
- **Priority**: 50  
- **Access**: Standard user operations

### Viewer (Level: "viewer")
- **Permissions**: ~8 permissions (read-only access)
- **Priority**: 25
- **Access**: View-only access

## Checking Default Permissions

### Get All Available Permissions
```bash
GET /v1/roles/available-permissions
```

### Get Default Permissions for Level
```bash
GET /v1/roles/default-permissions/admin
GET /v1/roles/default-permissions/moderator
GET /v1/roles/default-permissions/user
GET /v1/roles/default-permissions/viewer
```

## Role Updates

### Promoting to Admin
```bash
PATCH /v1/roles/:roleId
Authorization: Bearer <admin-token>

{
  "level": "admin"
  // Will automatically get ALL permissions
}
```

### Demoting from Admin
```bash
PATCH /v1/roles/:roleId
Authorization: Bearer <admin-token>

{
  "level": "moderator"
  // Will automatically get default moderator permissions
}
```

## Security Features

1. **Admin Protection**: Admin roles cannot have permissions removed
2. **Automatic Assignment**: No manual permission management needed for admins
3. **Level-Based Defaults**: Each level gets appropriate default permissions
4. **Override Protection**: Admin level always gets all permissions regardless of input

## Best Practices

1. **Use Admin Level Sparingly**: Only assign admin level to trusted users
2. **Use Appropriate Levels**: Choose the lowest level that provides needed access
3. **Custom Permissions**: Use custom permissions for specific use cases
4. **Regular Audits**: Review role assignments regularly

## Migration Notes

- Existing admin roles will be updated to have all permissions on next update
- No breaking changes to existing API endpoints
- Backward compatible with existing role assignments
