import { IDataServices, UserEntity } from '@app/repository';
import { CreateSkillDto } from '../dto';

export const create = async (
  user: UserEntity,
  data: CreateSkillDto,
  db: IDataServices,
  logoUrl?: string,
) => {
  // Parse tags if it's a string
  let tags = data.tags || [];
  if (typeof tags === 'string') {
    try {
      tags = JSON.parse(tags);
    } catch (e) {
      console.error('Error parsing tags string:', e);
      tags = [];
    }
  }

  const skillData = {
    ...data,
    user,
    logo: logoUrl || data.logo,
    tags: tags,
  };

  return await db.skills.insert(skillData);
};
