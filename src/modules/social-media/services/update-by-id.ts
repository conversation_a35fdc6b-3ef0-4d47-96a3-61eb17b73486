import { IDataServices } from '@app/repository';
import { UpdateSocialMediaDto } from '../dto';

export const updateById = async (
  id: string,
  data: UpdateSocialMediaDto,
  db: IDataServices,
  logoUrl?: string,
) => {
  const updateData = {
    ...data,
  };

  // Only update logo if we have a new one (either from Cloudinary or URL)
  if (logoUrl) {
    updateData.logo = logoUrl; // Store as array to match schema
  } else if (data.logo) {
    updateData.logo = data.logo;
  }
  // If neither logoUrl nor data.logo, don't update the logo field

  return await db.socialMedias.update({ id: id }, updateData);
};
