import { IDataServices, UserEntity } from '@app/repository';
import { ConfigPresenter } from '../presenter';
import { checkAdminRoleLevel } from '@app/common';
import { GetConfigPaginationDto } from '../dto';

export const getAll = async (
  db: IDataServices,
  user: UserEntity,
  paginationDto?: GetConfigPaginationDto,
) => {
  // Check if user exists and has an ID
  if (!user?.id) {
    return paginationDto
      ? {
          data: [],
          total: 0,
          page: paginationDto.page || 1,
          limit: paginationDto.limit || 10,
          totalPages: 0,
        }
      : [];
  }

  const hasAdminRole = await checkAdminRoleLevel(db, user.id);

  // Set up pagination parameters
  const page = paginationDto?.page || 1;
  const limit = paginationDto?.limit || 10;

  // Determine filter based on user role
  const filter = hasAdminRole ? {} : { user };

  // Always use pagination when pagination parameters are provided
  if (paginationDto && (paginationDto.page || paginationDto.limit)) {
    console.log(
      'Fetching configs with pagination for user:',
      user.id,
      'hasAdminRole:',
      hasAdminRole,
    );

    const paginationResult = await db.configs.findAllWithPagination(
      filter,
      page,
      limit,
    );

    console.log('Pagination result from database:', {
      total: paginationResult.total,
      page: paginationResult.page,
      totalPages: paginationResult.totalPages,
      dataLength: paginationResult.data.length,
    });

    // Apply presenter to all configs (don't filter by active here since we want to show all in admin)
    const configs = paginationResult.data.map(
      (config) => new ConfigPresenter(config),
    );

    console.log('Configs after presenter:', configs.length);

    return {
      data: configs,
      total: paginationResult.total,
      page: paginationResult.page,
      limit: paginationResult.limit,
      totalPages: paginationResult.totalPages,
    };
  }

  // Fallback to original non-paginated method for backward compatibility
  let data;
  if (hasAdminRole) {
    console.log('Fetching all configs for admin/moderator');
    data = await db.configs.findAll({});
  } else {
    console.log('Fetching configs for user:', user.id);
    data = await db.configs.findAll({ user });
  }

  console.log('Configs found:', data.length);

  // Apply presenter to all configs
  const configs = data.map((config) => new ConfigPresenter(config));

  console.log('Configs after presenter:', configs.length);

  return configs;
};
