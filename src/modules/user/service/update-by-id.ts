import { IDataServices, UserEntity } from '@app/repository';
import { UpdateUserDto } from '../dto/update-user.dto';

export const updateById = async (
  id: string,
  data: UpdateUserDto,
  db: IDataServices,
  logoUrl?: string,
) => {
  const updateData = {
    ...data,
    // If logoUrl is provided from Cloudinary upload, use it instead of the one in data
    profileImage: logoUrl || data.profileImage,
  };
  console.log(updateData);

  return await db.users.update(
    { id: id },
    updateData as unknown as Partial<UserEntity>,
  );
};
