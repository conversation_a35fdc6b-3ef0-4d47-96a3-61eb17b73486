import React, { type ReactElement } from 'react'

import dynamic from 'next/dynamic'

import Interest from '@views/interest'

const InterestTab = dynamic(() => import('@views/interest/interest'))
const AllTab = dynamic(() => import('@views/interest/all'))

const tabContentList = (): { [key: string]: ReactElement } => ({
  all: <AllTab />,
  interest: <InterestTab />
})

const Page = () => {
  return <Interest tabContentList={tabContentList()} />
}

export default Page
